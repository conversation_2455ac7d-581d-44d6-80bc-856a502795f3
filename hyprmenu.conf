## HyprMenu Configuration File
## This file controls the appearance and behavior of HyprMenu
## All color values support #RRGGBB, #RRGGBBAA, rgb(), or rgba() formats
[Layout]
#Width of the menu window in pixels
window_width=800
#Height of the menu window in pixels
window_height=600
#Margin from the top of the screen
top_margin=48
#Margin from the left of the screen
left_margin=8
#Whether to center the window on screen
center_window=false
#Position of the menu (top-left, top-center, top-right, bottom-left, bottom-center, bottom-right, center)
menu_position=bottom-center
#Offset from bottom for dock/panel (0 to respect reserved space)
bottom_offset=55
#Offset from top for panel
top_offset=48
#Internal padding of the main window
window_padding=8

[Window]
#Window background color (empty for transparent)
background_color=
#Overall window opacity (0.0 to 1.0)
background_opacity=0.90000000000000002
#Background blur strength
background_blur=5
#Window corner radius
corner_radius=5
#Horizontal alignment (start, center, end)
halign=center
#Vertical alignment (start, center, end)
valign=center
#Window shadow color
shadow_color=rgba(0,0,0,0.3)
#Window shadow radius
shadow_radius=20
#Overall window opacity
opacity=1

[Border]
#Color of the inner border
inner_border_color=#444444
#Radius of inner border corners
inner_border_radius=5
#Width of inner border
inner_border_width=2
#Color of the outer border
outer_border_color=rgba(178, 178, 178, 0.64)
#Radius of outer border corners
outer_border_radius=5
#Width of outer border
outer_border_width=2

[Grid]
#Number of columns in grid
columns=5
#Size of each grid item
item_size=100
#Corner radius of grid items
item_corner_radius=8
#Border width of grid items
item_border_width=1
#Border color of grid items
item_border_color=rgba(255,255,255,0.08)
#Background color of grid items
item_background_color=rgba(50,50,60,0.7)
#Vertical spacing between rows
row_spacing=12
#Horizontal spacing between columns
column_spacing=12
#Left margin
margin_start=12
#Right margin
margin_end=12
#Top margin
margin_top=12
#Bottom margin
margin_bottom=12
#Horizontal alignment of grid
halign=center
#Vertical alignment of grid
valign=center
#Whether grid expands horizontally
hexpand=true
#Whether grid expands vertically
vexpand=false
#Overall grid opacity
opacity=1
#Individual item opacity
item_opacity=1

[List]
#Height of each list item
item_size=48
#Corner radius of list items
item_corner_radius=6
#Border width of list items
item_border_width=1
#Border color of list items
item_border_color=rgba(255,255,255,0.05)
#Background color of list items
item_background_color=rgba(60,60,70,0.6)
#Vertical spacing between items
row_spacing=8
#Left margin
margin_start=12
#Right margin
margin_end=12
#Top margin
margin_top=12
#Bottom margin
margin_bottom=12
#Horizontal alignment of list
halign=fill
#Vertical alignment of list
valign=center
#Whether list expands horizontally
hexpand=true
#Whether list expands vertically
vexpand=false
#Overall list opacity
opacity=1
#Individual item opacity
item_opacity=1

[AppEntry]
#Size of application icons
icon_size=32
#Corner radius of icons
icon_corner_radius=6
#Background color behind icons
icon_background_color=rgba(60,60,70,0.6)
#Font size of application names
name_font_size=12
#Color of application names
name_color=#ffffff
#Font size of application descriptions
desc_font_size=10
#Color of application descriptions
desc_color=rgba(255,255,255,0.7)
#Internal padding of entries
padding=6
#Background color on hover
hover_color=rgba(100,100,100,0.8)
#Background color when clicked
active_color=rgba(100,100,100,0.9)
#Overall entry opacity
opacity=1
#Icon opacity
icon_opacity=1
#Name text opacity
name_opacity=1
#Description text opacity
desc_opacity=1

[Category]
#Category background color
background_color=#2d2d2d
#Category background opacity
background_opacity=1
#Corner radius of category headers
corner_radius=10
#Category text color
text_color=
#Category text size
font_size=13
#Category font family
font_family=Sans Bold
#Internal padding of categories
padding=6
#Whether to show separators between categories
show_separators=true
#Color of category separators
separator_color=rgba(255,255,255,0.1)
#Overall category opacity
opacity=1
#Category title opacity
title_opacity=1

[Search]
#Search bar background color
background_color=rgba(34, 34, 34, 0.3)
#Search bar background opacity
background_opacity=1
#Corner radius of search bar
corner_radius=5
#Search text color
text_color=
#Search text size
font_size=14
#Search text font
font_family=Sans
#Internal padding of search bar
padding=8
#Minimum height of search bar
min_height=20
#Left padding of search text
left_padding=2
#Maximum search text length (0 for unlimited)
length=0
#Placeholder text when empty
placeholder_text=Search applications...
#Size of search icon
icon_size=16
#Color of search icon
icon_color=rgba(255,255,255,0.7)
#Border color when focused
focus_border_color=rgba(255,255,255,0.2)
#Shadow color when focused
focus_shadow_color=rgba(0,0,0,0.1)
#Overall search bar opacity
opacity=1
#Search text opacity
text_opacity=1
#Search icon opacity
icon_opacity=1

[SystemButton]
#Button background color
background_color=rgba(60,60,70,0.6)
#Button icon color
icon_color=rgba(255,255,255,0.7)
#Background color on hover
hover_color=rgba(100,100,100,0.8)
#Background color when clicked
active_color=rgba(100,100,100,0.9)
#Corner radius of buttons
corner_radius=6
#Size of buttons
size=32
#Space between buttons
spacing=8
#Overall button opacity
opacity=1
#Button icon opacity
icon_opacity=1

[Behavior]
#Close when clicking outside the menu
close_on_click_outside=true
#Close when pressing Super key
close_on_super_key=true
#Close when launching an application
close_on_app_launch=true
#Focus search bar when opening
focus_search_on_open=true
#Close when pressing Escape
close_on_escape=true
#Close when losing focus
close_on_focus_out=true
#Show application categories
show_categories=true
#Show application descriptions
show_descriptions=true
#Show application icons
show_icons=true
#Show search bar
show_search=true
#Show scrollbar
show_scrollbar=true
#Show window border
show_border=true
#Show window shadow
show_shadow=true
#Enable background blur
blur_background=true
#Background blur strength
blur_strength=10
#Maximum number of recent apps to show
max_recent_apps=10

[Transparency]
#Enable transparency effects
enabled=true
#Global alpha value (0.0 to 1.0)
alpha=1
#Enable blur effects
blur=true
#Enable shadow effects
shadow=true
#Shadow color
shadow_color=rgba(0,0,0,0.3)
#Shadow radius
shadow_radius=20

[Hyprland]
#Enable corner artifact fix for Hyprland
use_hyprland_corner_fix=false
#Corner radius to use with Hyprland fix
hyprland_corner_radius=0
